# 🔧 Creating an EXE Launcher for Steam

If you want a true .exe file for the best Steam integration, here are several methods:

## 🚀 Method 1: PowerShell to EXE (Recommended)

### Using PS2EXE Module:
1. Open PowerShell as Administrator
2. Install PS2EXE module:
   ```powershell
   Install-Module -Name ps2exe -Force
   ```
3. Navigate to your game folder:
   ```powershell
   cd "C:\Path\To\Your\Game\Folder"
   ```
4. Convert the PowerShell script to EXE (use the simplified version):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokOnlineClicker_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile "icon.ico" -title "Ragnarok Online Clicker" -description "Ragnarok Online Clicker Game Launcher" -company "Your Name" -version "*******" -noConsole
   ```

### Result:
- Creates `Ragnarok Online Clicker.exe`
- No console window appears
- Perfect Steam integration
- Custom icon support

### Troubleshooting PS2EXE Compilation Errors:

**If you get compilation errors, try these scripts in order:**

1. **Ultra Simple Version with Icon** (most likely to work):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Ultra_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile "icon.ico" -noConsole
   ```

2. **Basic Version with Icon** (if ultra simple doesn't work):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Basic.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile "icon.ico" -noConsole
   ```

3. **With full metadata and icon**:
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Ultra_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile "icon.ico" -title "Ragnarok Online Clicker" -description "Ragnarok Online Clicker Game Launcher" -company "Your Name" -version "*******" -noConsole
   ```

4. **Alternative icon path format** (if icon still fails):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Ultra_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile ".\icon.ico" -noConsole
   ```

5. **Without icon** (fallback if icon causes issues):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Ultra_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -title "Ragnarok Online Clicker" -noConsole
   ```

6. **With verbose output** (to see detailed errors):
   ```powershell
   Invoke-ps2exe -inputFile "RagnarokLauncher_Ultra_Simple.ps1" -outputFile "Ragnarok Online Clicker.exe" -iconFile "icon.ico" -verbose
   ```

## 🚀 Method 2: Batch to EXE Converter

### Using Bat To Exe Converter (Free Tool):
1. Download "Bat To Exe Converter" from: https://www.f2ko.de/en/b2e.php
2. Open the converter
3. Load `RagnarokOnlineClicker.bat`
4. Set output name: "Ragnarok Online Clicker.exe"
5. Add custom icon if desired
6. Set to "Invisible application" (no console)
7. Click "Compile"

### Result:
- Creates standalone EXE file
- No dependencies required
- Works on any Windows system

## 🚀 Method 3: AutoHotkey (Advanced)

### Create AHK Script:
1. Install AutoHotkey from: https://www.autohotkey.com/
2. Create `RagnarokLauncher.ahk`:
   ```autohotkey
   #NoEnv
   #SingleInstance Force
   
   ; Get script directory
   SplitPath, A_ScriptDir,, GameDir
   IndexPath := GameDir . "\index.html"
   
   ; Check if index.html exists
   IfNotExist, %IndexPath%
   {
       MsgBox, 16, Error, index.html not found in %GameDir%
       ExitApp
   }
   
   ; Convert to file URL
   StringReplace, FileURL, IndexPath, \, /, All
   FileURL := "file:///" . FileURL
   
   ; Try browsers
   RunWait, chrome.exe --new-window --app="%FileURL%",, Hide UseErrorLevel
   if ErrorLevel
   {
       RunWait, msedge.exe --new-window --app="%FileURL%",, Hide UseErrorLevel
       if ErrorLevel
       {
           Run, %IndexPath%
       }
   }
   ExitApp
   ```
3. Right-click the AHK file → "Compile Script"
4. Creates `RagnarokLauncher.exe`

## 🚀 Method 4: Simple Shortcut (Easiest)

### Create Windows Shortcut:
1. Right-click in game folder → New → Shortcut
2. For target, enter:
   ```
   powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File "RagnarokOnlineClicker.ps1"
   ```
3. Name it "Ragnarok Online Clicker"
4. Right-click shortcut → Properties → Change Icon
5. Add the shortcut to Steam instead of the script

## 📋 Comparison

| Method | Pros | Cons | Difficulty |
|--------|------|------|------------|
| **VBS Script** | Simple, works everywhere | Not a true EXE | Easy |
| **PS2EXE** | True EXE, professional | Requires PowerShell module | Medium |
| **Bat2Exe** | Simple converter, standalone | Third-party tool needed | Easy |
| **AutoHotkey** | Very customizable | Requires AHK installation | Medium |
| **Shortcut** | No extra tools needed | Still shows as shortcut | Easy |

## 🎯 Recommendation

For most users: **Use the VBS script** - it works perfectly with Steam and requires no additional tools.

For advanced users who want a true EXE: **Use PS2EXE method** - creates a professional executable with custom icon support.

## 🔧 Steam Integration Steps

Once you have your preferred launcher:

1. **Add to Steam**: Games → Add Non-Steam Game → Browse → Select your launcher
2. **Rename**: Right-click in Steam → Properties → Change name to "Ragnarok Online Clicker"
3. **Add Icon**: Properties → Click icon area → Browse to your icon file
4. **Test**: Launch from Steam to verify it works correctly

## ✅ Final Result

Your Steam library will show:
- **Proper Name**: "Ragnarok Online Clicker"
- **Custom Icon**: Your chosen game icon
- **Clean Launch**: No console windows or errors
- **Playtime Tracking**: Steam tracks your game time
- **Screenshots**: Steam overlay works (browser dependent)

The game will launch seamlessly from Steam just like any other game in your library!
