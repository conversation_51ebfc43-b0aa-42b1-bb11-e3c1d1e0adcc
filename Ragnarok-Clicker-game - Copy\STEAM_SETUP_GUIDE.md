# 🎮 Adding Ragnarok Online Clicker to Steam

This guide will help you add Ragnarok Online Clicker to your Steam library so you can launch it directly from Steam.

## 📋 Prerequisites

- Steam installed and running
- Ragnarok Online Clicker game files in a folder
- Windows 10/11

## 🚀 Method 1: VBS Script (Recommended - Best Steam Compatibility)

### Step 1: Prepare the VBS Launcher
1. Make sure `Ragnarok Online Clicker.vbs` is in the same folder as `index.html`
2. Test the launcher by double-clicking `Ragnarok Online Clicker.vbs`
3. Verify the game opens correctly in your browser (should launch silently)

### Step 2: Add to Steam
1. Open Steam
2. Click "Games" in the top menu
3. Select "Add a Non-Steam Game to My Library..."
4. Click "Browse..." button
5. Navigate to your Ragnarok Online Clicker folder
6. **Change file type filter to "All Files (*.*)"** at the bottom
7. Select `Ragnarok Online Clicker.vbs`
8. Click "Open"
9. Make sure the checkbox next to the launcher is checked
10. Click "Add Selected Programs"

## 🚀 Method 2: Improved Batch File (Alternative)

### Step 1: Prepare the Batch Launcher
1. Make sure `RagnarokOnlineClicker.bat` is in the same folder as `index.html`
2. Test the launcher by double-clicking `RagnarokOnlineClicker.bat`
3. Verify the game opens correctly in your browser

### Step 2: Add to Steam
1. Open Steam
2. Click "Games" in the top menu
3. Select "Add a Non-Steam Game to My Library..."
4. Click "Browse..." button
5. Navigate to your Ragnarok Online Clicker folder
6. Select `RagnarokOnlineClicker.bat`
7. Click "Open"
8. Make sure the checkbox next to the launcher is checked
9. Click "Add Selected Programs"

### Step 3: Customize the Steam Entry
1. Right-click on "launch_ragnarok_clicker" in your Steam library
2. Select "Properties"
3. Change the name to "Ragnarok Online Clicker"
4. Optionally add a custom icon:
   - Click the icon area in the properties window
   - Browse to select an icon file (you can use any .ico file)
5. Click "Close"

## 🎨 Method 2: Custom Steam Grid Art (Optional)

To make your game look professional in Steam:

1. Right-click on the game in your Steam library
2. Select "Set Custom Artwork"
3. Choose from:
   - **Grid Image**: 460x215 pixels (for library grid view)
   - **Hero Image**: 1920x620 pixels (for library detail view)
   - **Logo**: Transparent PNG (for overlay on hero image)

## ⚡ Method 3: PowerShell Launcher (Alternative)

If you prefer PowerShell:

1. Right-click on `launch_ragnarok_clicker.ps1`
2. Select "Properties"
3. In the "Target" field, add:
   ```
   powershell.exe -ExecutionPolicy Bypass -File "launch_ragnarok_clicker.ps1"
   ```
4. Follow the same Steam setup steps but select the PowerShell script

## 🔧 Advanced: Steam Launch Options

For more control, you can set custom launch options:

1. Right-click the game in Steam → Properties
2. In "Launch Options" field, add:
   ```
   --new-window --app
   ```
   (This works if you're launching Chrome directly)

## 🎯 Launch Parameters

The batch file will try browsers in this order:
1. **Chrome** (with app mode for fullscreen-like experience)
2. **Edge** (with app mode)
3. **Firefox** (regular window)
4. **Default browser** (fallback)

## 📱 Steam Deck Compatibility

To run on Steam Deck:
1. Add the game as a non-Steam game
2. Set compatibility tool to "Proton Experimental"
3. The game should run in the Steam Deck's browser

## 🛠️ Troubleshooting

### Game Won't Launch
- Verify `index.html` is in the same folder as the launcher
- Check that you have a compatible browser installed
- Try running the launcher file manually first
- For VBS: Make sure Windows Script Host is enabled

### Steam Shows Garbled Characters or Wrong Name
- **Use the VBS file instead of BAT file** - this fixes character encoding issues
- Right-click the game in Steam → Properties → Change the name to "Ragnarok Online Clicker"
- If still having issues, try the PowerShell launcher

### Can't Find the Launcher in Steam's File Browser
- Make sure to change the file type filter to "All Files (*.*)" when browsing
- VBS and BAT files might be hidden by default file filters

### No Icon in Steam
- Download a .ico file for Ragnarok Online
- Set it in Properties → Icon
- Use the `create_icon.html` file to generate a custom icon

### Browser Opens But Game Doesn't Load
- Check browser console for errors (F12)
- Ensure all game files are present
- Try a different browser
- Clear browser cache and try again

### VBS Script Security Warning
- If Windows blocks the VBS script, right-click → Properties → Unblock
- Or run as administrator once to establish trust

## 🎮 Enjoy!

Once set up, you can:
- Launch the game directly from Steam
- Use Steam overlay (if supported by browser)
- Track playtime
- Take screenshots with Steam
- Stream the game to friends

The game will appear in your Steam library just like any other game!
