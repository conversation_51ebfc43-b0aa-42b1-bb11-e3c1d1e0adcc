<!DOCTYPE html>
<html>
<head>
    <title>Ragnarok Online Clicker - Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .icon-preview {
            width: 256px;
            height: 256px;
            background: linear-gradient(135deg, #4fc3f7, #29b6f6);
            border-radius: 20px;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 120px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            border: 4px solid #ffd700;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        .instructions {
            max-width: 600px;
            margin: 0 auto;
            text-align: left;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        button {
            background: #4fc3f7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #29b6f6;
        }
    </style>
</head>
<body>
    <h1>🎮 Ragnarok Online Clicker</h1>
    <h2>Steam Icon Generator</h2>
    
    <div class="icon-preview" id="iconPreview">⚔️</div>
    
    <button onclick="downloadIcon()">Download Icon</button>
    <button onclick="changeIcon()">Change Icon</button>
    
    <div class="instructions">
        <h3>📋 How to use this icon:</h3>
        <ol>
            <li>Click "Download Icon" to save the icon image</li>
            <li>Follow the Steam setup guide to add the game to Steam</li>
            <li>In Steam, right-click the game → Properties</li>
            <li>Click the icon area and select the downloaded image</li>
            <li>Enjoy your custom Steam game icon!</li>
        </ol>
        
        <h3>🎨 Icon Options:</h3>
        <p>Click "Change Icon" to cycle through different emoji icons:</p>
        <ul>
            <li>⚔️ Sword (Default)</li>
            <li>🛡️ Shield</li>
            <li>👑 Crown</li>
            <li>🏰 Castle</li>
            <li>⭐ Star</li>
            <li>🎮 Game Controller</li>
        </ul>
    </div>

    <script>
        const icons = ['⚔️', '🛡️', '👑', '🏰', '⭐', '🎮'];
        let currentIconIndex = 0;

        function changeIcon() {
            currentIconIndex = (currentIconIndex + 1) % icons.length;
            document.getElementById('iconPreview').textContent = icons[currentIconIndex];
        }

        function downloadIcon() {
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#4fc3f7');
            gradient.addColorStop(1, '#29b6f6');
            
            // Draw background
            ctx.fillStyle = gradient;
            ctx.roundRect(0, 0, 256, 256, 20);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = '#ffd700';
            ctx.lineWidth = 8;
            ctx.roundRect(4, 4, 248, 248, 16);
            ctx.stroke();
            
            // Draw icon
            ctx.font = '120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText(icons[currentIconIndex], 128, 128);

            // Download the image
            const link = document.createElement('a');
            link.download = 'ragnarok_clicker_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // Add roundRect method for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
