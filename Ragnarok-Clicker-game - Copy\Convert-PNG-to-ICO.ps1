# Convert PNG to ICO for ps2exe
# Usage: .\Convert-PNG-to-ICO.ps1 -PngPath "your-image.png" -IcoPath "icon.ico"

param(
    [string]$PngPath = "icon.png",
    [string]$IcoPath = "icon.ico"
)

Add-Type -AssemblyName System.Drawing

try {
    # Load the PNG image
    $png = [System.Drawing.Image]::FromFile((Resolve-Path $PngPath).Path)
    
    # Create a new bitmap with the desired size (32x32 is standard for exe icons)
    $bitmap = New-Object System.Drawing.Bitmap(32, 32)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set high quality rendering
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    
    # Draw the PNG onto the bitmap, resizing it to 32x32
    $graphics.DrawImage($png, 0, 0, 32, 32)
    
    # Save as ICO
    $bitmap.Save($IcoPath, [System.Drawing.Imaging.ImageFormat]::Icon)
    
    # Clean up
    $graphics.Dispose()
    $bitmap.Dispose()
    $png.Dispose()
    
    Write-Host "✅ Successfully converted $PngPath to $IcoPath" -ForegroundColor Green
    Write-Host "You can now use: -iconFile `"$IcoPath`"" -ForegroundColor Yellow
}
catch {
    Write-Host "❌ Error converting image: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Try using an online converter instead." -ForegroundColor Yellow
}
