# Basic Ragnarok Online Clicker Launcher - Minimal for ps2exe
# This version avoids common ps2exe compilation issues

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Build path to index.html
$IndexFile = Join-Path $ScriptDir "index.html"

# Check if file exists
if (!(Test-Path $IndexFile)) {
    [System.Reflection.Assembly]::LoadWithPartialName("System.Windows.Forms") | Out-Null
    [System.Windows.Forms.MessageBox]::Show("Game file not found: $IndexFile", "Error", "OK", "Error")
    exit 1
}

# Create file URL
$FileURL = "file:///" + $IndexFile.Replace('\', '/')

# Launch with Chrome
$ChromeProcess = Start-Process -FilePath "chrome.exe" -ArgumentList "--new-window","--app=$FileURL" -PassThru -ErrorAction SilentlyContinue

if ($ChromeProcess) {
    exit 0
}

# Launch with Edge if Chrome failed
$EdgeProcess = Start-Process -FilePath "msedge.exe" -ArgumentList "--new-window","--app=$FileURL" -PassThru -ErrorAction SilentlyContinue

if ($EdgeProcess) {
    exit 0
}

# Launch with default browser if others failed
Start-Process -FilePath $IndexFile -ErrorAction SilentlyContinue
exit 0
