# Ragnarok Online Clicker Launcher - Simplified for EXE conversion
# This version is optimized for ps2exe conversion

param()

# Get the directory where this script/exe is located
$GameDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Check if index.html exists
$IndexPath = Join-Path $GameDir "index.html"
if (-not (Test-Path $IndexPath)) {
    Add-Type -AssemblyName System.Windows.Forms
    [System.Windows.Forms.MessageBox]::Show(
        "ERROR: index.html not found in $GameDir`n`nPlease make sure this launcher is in the same folder as index.html",
        "Ragnarok Online Clicker - Error",
        [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]::Error
    )
    exit 1
}

# Convert to file:// URL
$FileUrl = "file:///" + $IndexPath.Replace('\', '/')

# Try Chrome first
try {
    $chromeArgs = "--new-window", "--app=$FileUrl"
    Start-Process "chrome.exe" -ArgumentList $chromeArgs -ErrorAction Stop
    exit 0
}
catch {
    # Chrome failed, try Edge
    try {
        $edgeArgs = "--new-window", "--app=$FileUrl"
        Start-Process "msedge.exe" -ArgumentList $edgeArgs -ErrorAction Stop
        exit 0
    }
    catch {
        # Edge failed, try Firefox
        try {
            Start-Process "firefox.exe" -ArgumentList $FileUrl -ErrorAction Stop
            exit 0
        }
        catch {
            # All browsers failed, use default
            try {
                Start-Process $IndexPath
                exit 0
            }
            catch {
                Add-Type -AssemblyName System.Windows.Forms
                [System.Windows.Forms.MessageBox]::Show(
                    "Failed to launch Ragnarok Online Clicker`n`nPlease check that you have a web browser installed.",
                    "Ragnarok Online Clicker - Launch Error",
                    [System.Windows.Forms.MessageBoxButtons]::OK,
                    [System.Windows.Forms.MessageBoxIcon]::Error
                )
                exit 1
            }
        }
    }
}
