<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Modern Ragnarok Online Clicker RPG</title>
   <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        :root {
            --primary-bg: #0a0f1c;
            --secondary-bg: #1a1f3a;
            --glass-bg: rgba(26, 31, 58, 0.7);
            --border-color: rgba(79, 195, 247, 0.3);
            --gold: #ffd700;
            --blue: #4fc3f7;
            --red: #ff6b6b;
            --mp-blue: #1e88e5;
            --text-primary: #ffffff;
            --text-secondary: #b0b3c1;
            --shadow-glow: 0 0 30px rgba(79, 195, 247, 0.3);
            --shadow-strong: 0 10px 30px rgba(0, 0, 0, 0.5);
            --field-color: #4caf50;
            --dungeon-color: #ff9800;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e1b4b 50%, var(--secondary-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 10px;
            overflow-x: hidden;
            position: relative;
            touch-action: manipulation;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(79, 195, 247, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(156, 39, 176, 0.1) 0%, transparent 50%);
            animation: backgroundShift 15s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: translateX(0) translateY(0); }
            33% { transform: translateX(-10px) translateY(10px); }
            66% { transform: translateX(10px) translateY(-10px); }
        }

        /* === PREVENT TEXT SELECTION ON INTERACTIVE ELEMENTS === */
        button,
        .button-learn,
        .action-button,
        .characters-button,
        .combat-button,
        .hotbar-slot,
        .inventory-slot,
        .equipment-slot,
        .shop-item,
        .skill-item,
        .panel-title,
        .dropdown-arrow,
        .page-btn,
        .close-btn,
        .create-character-btn,
        .skill-hotbar,
        .skill-name,
        .skill-description,
        .skill-level,
        .skill-cost,
        .location-panel-compact,
        .location-selector,
        .stat-item,
        .stat-label,
        .stat-value,
        #location-info,
        #progression-panel,
        #enhanced-location-info,
        #enhanced-progression-info,
        #enhanced-status-list,
        [onclick],
        [role="button"],
        [data-onclick] {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* === PREVENT MULTIPLE SELECTION AND DRAGGING === */
        .skill-item,
        .inventory-slot,
        .equipment-slot,
        .shop-item,
        .hotbar-slot {
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Prevent text selection on drag */
        *::selection {
            background: transparent;
        }

        *::-moz-selection {
            background: transparent;
        }

        .game-container {
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
            backdrop-filter: blur(20px);
            background: var(--glass-bg);
            border-radius: 16px;
            padding: 16px;
            box-shadow: var(--shadow-strong);
            border: 1px solid var(--border-color);
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }

        .header h1 {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 700;
            background: linear-gradient(135deg, var(--gold), #ffed4e, var(--blue));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }

        .characters-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, var(--blue), #29b6f6);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
            min-height: 44px;
            min-width: 44px;
        }

        /* === NEW MAIN LAYOUT === */
        .main-layout-new {
            display: grid;
            grid-template-columns: minmax(300px, 350px) minmax(500px, 2fr) minmax(350px, 400px);
            gap: 16px;
            margin-bottom: 16px;
            /* Remove fixed height so content can flow naturally */
            max-width: 1600px;
            margin: 0 auto 16px auto;
            padding: 0 10px;
}

        /* === CENTER COLUMN LAYOUT === */
        .center-column-new {
            display: flex;
            flex-direction: column;
            gap: 8px;
            /* Remove height: 100% to allow natural content flow */
        }

        .location-panel-compact, .player-status-compact {
            padding: 12px;
            margin-bottom: 0;
        }

        .location-panel-compact h2, .player-status-compact h2 {
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .battle-area-main {
            flex: 1;
            min-height: 350px;
            position: relative;
        }

        /* === HOTBAR UNDER COMBAT === */
        .hotbar-section {
            display: flex;
            justify-content: center;
            padding: 8px 0;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            margin: 4px 0;
        }

        .hotbar-section .skill-hotbar {
            position: relative !important;
            bottom: auto !important;
            left: auto !important;
            transform: none !important;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 8px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            display: flex;
            gap: 8px;
        }

        .skills-panel-compact {
            max-height: 360px;
            min-height: 360px; /* Force the panel to be at least 360px tall */
            overflow-y: auto;
            padding: 12px;
            flex-shrink: 0; /* Prevent the panel from shrinking in the flex layout */
        }

        .skills-panel-compact h2 {
            margin-bottom: 8px;
            font-size: 1rem;
        }

        /* === INVENTORY WITH PAGINATION === */
        .inventory-panel-paginated {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 16px;
            align-items: center;
        }

        .inventory-nav {
            display: flex;
            gap: 4px;
            margin-bottom: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .page-btn {
            background: rgba(79, 195, 247, 0.2);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 44px;
            min-width: 44px;
            font-weight: 600;
        }

        .page-btn:hover {
            background: rgba(79, 195, 247, 0.4);
            border-color: var(--blue);
            transform: translateY(-1px);
        }

        .page-btn.active {
            background: var(--blue);
            color: white;
            border-color: var(--blue);
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
        }

        .page-btn.has-items {
            border-color: var(--gold);
            background: rgba(255, 215, 0, 0.1);
        }

        .page-btn.has-items.active {
            background: var(--gold);
            color: #000;
            border-color: var(--gold);
        }

        .page-btn.favorites-btn {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            border-color: #ffd700;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .page-btn.favorites-btn:hover {
            background: linear-gradient(135deg, #ffed4e, #ffd700);
            transform: scale(1.05);
        }

        .page-btn.favorites-btn.active {
            background: linear-gradient(135deg, #ffb300, #ffd700);
            color: #333;
            border-color: #ffb300;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        /* Fix dropdown styling to ensure dark background */
        select {
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            border: 1px solid var(--border) !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        select option {
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            padding: 4px 8px !important;
        }

        select option:hover {
            background: var(--blue) !important;
            color: white !important;
        }

        select option:checked,
        select option:focus,
        select option[selected] {
            background: var(--blue) !important;
            color: white !important;
        }

        /* Ensure dropdown list has dark background */
        select:focus {
            background: var(--bg-secondary) !important;
            outline: 1px solid var(--blue);
        }

        /* Force dark background on dropdown list */
        select[size],
        select[multiple] {
            background: var(--bg-secondary) !important;
        }

        /* Browser-specific fixes */
        @-moz-document url-prefix() {
            select option {
                background-color: var(--bg-secondary) !important;
                color: var(--text-primary) !important;
            }
        }

        /* === 64x64 INVENTORY GRID - 6x8 Layout === */
.inventory-grid-paginated {
    display: grid;
    grid-template-columns: repeat(6, 64px); /* 6 columns of 64px */
    grid-template-rows: repeat(8, 64px);    /* 8 rows of 64px */
    gap: 4px;
    margin-bottom: 12px;
    width: 404px;  /* 6 * 64px + 5 * 4px gap = 404px */
    height: 540px; /* 8 * 64px + 7 * 4px gap = 540px */
    justify-content: center;
    overflow: hidden;
}

.inventory-grid-paginated .inventory-slot {
    width: 64px !important;
    height: 64px !important;
    background: var(--glass-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    touch-action: manipulation;
}

.inventory-grid-paginated .item {
    width: 60px !important;
    height: 60px !important;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
    position: relative;
    transition: all 0.2s ease;
}

.inventory-grid-paginated .item img {
    width: 60px !important;
    height: 60px !important;
    object-fit: contain !important;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: -webkit-crisp-edges;
}

.inventory-grid-paginated .item:not(:has(img)) {
    font-size: 2rem !important;
    line-height: 1;
}

.inventory-grid-paginated .item-count {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 1px 4px;
    border-radius: 3px;
    border: 1px solid var(--border-color);
    min-width: 12px;
    text-align: center;
}

.inventory-grid-paginated .inventory-slot:hover {
    background: rgba(79, 195, 247, 0.15);
    transform: scale(1.05);
    border-color: var(--blue);
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
    z-index: 10;
}

.inventory-grid-paginated .inventory-slot.drag-over {
    border-color: var(--gold) !important;
    background: rgba(255, 215, 0, 0.15) !important;
    transform: scale(1.05);
}

.inventory-grid-paginated .item.dragging {
    opacity: 0.6;
    cursor: grabbing;
    transform: scale(1.1);
    z-index: 100;
}

/* Mobile responsive - smaller grids */
@media (max-width: 768px) {
    body {
        padding: 5px;
    }
    
    .game-container {
        padding: 10px;
        border-radius: 12px;
    }
    
    .main-layout-new {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 12px;
        padding: 0 5px;
        /* Remove height constraint for mobile */
    }
    
    .center-column-new {
        order: 1;
    }
    
    .stats-panel {
        order: 2;
    }
    
    .inventory-panel-paginated {
        order: 3;
    }
    
    .shop-section-bottom {
        margin: 12px auto 0 auto;
        padding: 0 5px;
    }
    
    .shop-items-bottom {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        max-height: 250px;
    }
    
    .shop-panel-bottom {
        margin-bottom: 15px;
    }
    
    /* Keep all your existing mobile inventory styles */
    .inventory-grid-paginated {
        grid-template-columns: repeat(5, 56px);
        grid-template-rows: repeat(8, 56px);
        width: 300px;
        height: 464px;
    }
    
    .inventory-grid-paginated .inventory-slot {
        width: 56px !important;
        height: 56px !important;
    }
    
    .inventory-grid-paginated .item {
        width: 52px !important;
        height: 52px !important;
    }
    
    .inventory-grid-paginated .item img {
        width: 52px !important;
        height: 52px !important;
    }
    
    .inventory-grid-paginated .item:not(:has(img)) {
        font-size: 1.8rem !important;
    }
    
    /* Keep the rest of your existing mobile styles */
    .hotbar-section .skill-hotbar {
        padding: 6px;
        gap: 4px;
    }
    
    .page-btn {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
    
    .inventory-nav {
        gap: 2px;
    }
    
    /* ... rest of your existing mobile styles ... */
}

/* Landscape mobile - 6x6 grid */
@media (max-width: 768px) and (orientation: landscape) {
    .inventory-grid-paginated {
        grid-template-columns: repeat(6, 48px);
        grid-template-rows: repeat(6, 48px);
        width: 308px;
        height: 308px;
    }
    
    .center-column-new, .stats-panel, .inventory-panel-paginated {
        order: initial;
    }    

    .inventory-grid-paginated .inventory-slot {
        width: 48px !important;
        height: 48px !important;
    }
    
    .inventory-grid-paginated .item {
        width: 44px !important;
        height: 44px !important;
    }
    
    .inventory-grid-paginated .item img {
        width: 44px !important;
        height: 44px !important;
    }

     /* ADD THESE NEW SHOP STYLES: */
    .shop-section-bottom {
        margin: 16px auto 0 auto;
    }
    
    .shop-items-bottom {
        grid-template-columns: repeat(4, 1fr); /* More columns in landscape */
        gap: 8px;
        max-height: 200px; /* Shorter height for landscape */
    }
}

        /* === SHOP AT BOTTOM === */
    .shop-section-bottom {
    width: 100%;
    max-width: 1600px;
    margin: 16px auto 0 auto; /* Add top margin, remove bottom positioning */
    padding: 0 10px;
    position: relative; /* Remove any fixed positioning */
}

    .shop-panel-bottom {
    padding: 16px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-strong);
    transition: all 0.3s ease;
    margin-bottom: 20px; /* Add some bottom spacing */
}

    .shop-panel-bottom h2 {
    color: var(--gold);
    margin-bottom: 16px;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    position: relative;
    padding-bottom: 8px;
}

    .shop-panel-bottom h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--gold), transparent);
}

    .shop-items-bottom {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    max-height: 300px; /* Limit height but allow scrolling */
    overflow-y: auto;
}

    .shop-items-bottom::-webkit-scrollbar {
    width: 6px;
}

    .shop-items-bottom::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

    .shop-items-bottom::-webkit-scrollbar-thumb {
    background: var(--blue);
    border-radius: 3px;
}

        /* === COMMON PANEL STYLES === */
        .panel {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border-radius: 16px;
            padding: 16px;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-strong);
            transition: all 0.3s ease;
        }

        /* === BATTLE AREA SPECIFIC STYLES === */
        .battle-area {
            min-height: 600px; /* Ensure consistent height to prevent layout shifts */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .panel:hover {
            border-color: rgba(79, 195, 247, 0.6);
            box-shadow: var(--shadow-glow);
        }

        .panel h2 {
            color: var(--gold);
            margin-bottom: 16px;
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            position: relative;
            padding-bottom: 8px;
        }

        .panel h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--gold), transparent);
        }

        /* === PLAYER STATS === */
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 10px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            min-height: 44px;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .stat-value {
            color: var(--blue);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .gold-display {
            background: linear-gradient(135deg, var(--gold), #ffed4e);
            color: #1a1a2e;
            padding: 12px 16px;
            border-radius: 12px;
            text-align: center;
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 16px;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        /* === EQUIPMENT === */
        .equipment-slots {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }

        .equipment-slot {
            background: var(--glass-bg);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            min-height: 70px;
            position: relative;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            touch-action: manipulation;
        }

        .equipment-slot:hover {
            border-color: var(--blue);
            background: rgba(79, 195, 247, 0.1);
        }

        .equipment-slot.drag-over {
            border-color: var(--gold);
            background: rgba(255, 215, 0, 0.15);
            transform: scale(1.05);
        }

        .equipment-slot.filled {
            border-style: solid;
            border-color: var(--blue);
            background: rgba(79, 195, 247, 0.1);
        }

        /* === EQUIPMENT UPGRADE SECTION === */
        .upgrade-section {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 16px;
        }

        .upgrade-slot {
            background: var(--glass-bg);
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
        }

        .upgrade-slot:hover {
            border-color: var(--blue);
            background: rgba(79, 195, 247, 0.1);
        }

        .upgrade-slot.drag-over {
            border-color: var(--gold);
            background: rgba(255, 215, 0, 0.15);
            transform: scale(1.02);
        }

        .upgrade-slot.filled {
            border-style: solid;
            border-color: var(--gold);
            background: rgba(255, 215, 0, 0.1);
        }

        .upgrade-info {
            margin-top: 8px;
            font-size: 0.9rem;
        }

        .upgrade-stats {
            color: var(--text-primary);
            margin-bottom: 4px;
            font-weight: bold;
        }

        .upgrade-cost {
            color: var(--gold);
            margin-bottom: 4px;
        }

        .upgrade-chance {
            color: var(--text-secondary);
        }

        .upgrade-button {
            background: linear-gradient(135deg, var(--gold), #f39c12);
            color: #000;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .upgrade-button:hover:not(:disabled) {
            background: linear-gradient(135deg, #f39c12, var(--gold));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .upgrade-button:disabled {
            background: var(--glass-bg);
            color: var(--text-secondary);
            cursor: not-allowed;
            opacity: 0.5;
        }

        .upgrade-success {
            animation: upgradeSuccess 0.6s ease-out;
        }

        .upgrade-fail {
            animation: upgradeFail 0.6s ease-out;
        }

        @keyframes upgradeSuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); background: rgba(76, 175, 80, 0.3); }
            100% { transform: scale(1); }
        }

        @keyframes upgradeFail {
            0% { transform: scale(1); }
            25% { transform: translateX(-5px); background: rgba(244, 67, 54, 0.3); }
            75% { transform: translateX(5px); }
            100% { transform: scale(1); }
        }

        .slot-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-weight: 500;
        }

        /* === PLAYER BARS === */
        .player-bars-container {
            margin-bottom: 16px;
        }

        .player-hp-label, .player-mp-label {
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            margin-bottom: 6px;
            font-weight: 600;
        }

        .player-hp-label {
            color: var(--red);
        }

        .player-mp-label {
            color: var(--mp-blue);
        }

        .player-hp-bar, .player-mp-bar {
            width: 100%;
            height: clamp(28px, 5vw, 35px);
            background: rgba(0, 0, 0, 0.4);
            border-radius: 18px;
            overflow: hidden;
            position: relative;
            border: 2px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 10px;
        }

        .player-hp-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--red), #ff6b6b);
            transition: width 0.5s ease;
            border-radius: 16px;
        }

        .player-mp-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--mp-blue), #2196f3);
            transition: width 0.5s ease;
            border-radius: 16px;
        }

        .player-hp-text, .player-mp-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: 600;
            font-size: clamp(0.8rem, 2vw, 0.95rem);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            z-index: 2;
        }

        .exp-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            overflow: hidden;
            margin: 8px 0;
            border: 1px solid var(--border-color);
        }

        .exp-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--blue), #29b6f6);
            transition: width 0.5s ease;
            border-radius: 9px;
        }

        /* === LOCATION SELECTOR === */
        .location-selector {
            margin-bottom: 16px;
            position: relative;
        }

        .location-selector select {
            width: 100%;
            padding: 12px 16px;
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 12px;
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            min-height: 44px;
        }

        .location-selector select:hover {
            border-color: var(--blue);
            box-shadow: 0 0 15px rgba(79, 195, 247, 0.3);
        }

        .location-selector option {
            background: var(--secondary-bg);
            color: var(--text-primary);
            padding: 8px;
        }

        .location-selector optgroup {
            font-weight: bold;
            font-size: 0.9rem;
            padding: 4px 0;
        }

        .location-selector optgroup[label*="Fields"] {
            color: var(--field-color);
        }

        .location-selector optgroup[label*="Dungeons"] {
            color: var(--dungeon-color);
        }

        .location-selector optgroup[label*="Advanced"] {
            color: var(--red);
        }

        /* === HOTBAR SKILLS === */
        .hotbar-skill {
            width: clamp(50px, 12vw, 70px);
            height: clamp(50px, 12vw, 70px);
            background: var(--glass-bg);
            border: 2px solid var(--border-color);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            touch-action: manipulation;
            min-height: 44px;
            min-width: 44px;
        }

        .hotbar-skill:hover, .hotbar-skill:active {
            background: rgba(79, 195, 247, 0.2);
            transform: translateY(-2px) scale(1.05);
            border-color: var(--blue);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
        }

        .hotbar-skill.on-cooldown {
            cursor: not-allowed;
            opacity: 0.4;
            filter: grayscale(0.8);
        }

        .hotbar-skill-icon {
            font-size: clamp(1.2rem, 4vw, 2rem);
            margin-bottom: 2px;
        }

        .hotbar-skill-key {
            position: absolute;
            top: 2px;
            left: 4px;
            font-size: clamp(0.6rem, 1.5vw, 0.75rem);
            color: var(--gold);
            font-weight: 700;
            background: rgba(0, 0, 0, 0.7);
            padding: 1px 4px;
            border-radius: 3px;
        }

        .cooldown-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-weight: 700;
            color: var(--text-primary);
            font-size: clamp(0.9rem, 3vw, 1.3rem);
        }

        .hotbar-skill-mp {
            position: absolute;
            top: 2px;
            left: 2px;
            background: var(--mp-blue);
            color: white;
            font-size: clamp(0.6rem, 1.5vw, 0.7rem);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }

        .hotbar-skill.no-mp {
            opacity: 0.4;
            cursor: not-allowed;
        }

        .hotbar-skill.no-mp .hotbar-skill-mp {
            background: #f44336;
        }

        /* === SKILLS === */
        .skills-container {
            display: grid;
            gap: 8px;
            margin-top: 16px;
            max-height: 360px; /* Match the panel height */
            overflow-y: auto;
            padding-right: 4px;
        }

        .skills-container::-webkit-scrollbar {
            width: 4px;
        }

        .skills-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .skills-container::-webkit-scrollbar-thumb {
            background: var(--blue);
            border-radius: 2px;
        }

        .skill-item {
            background: var(--glass-bg);
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            backdrop-filter: blur(10px);
            min-height: 44px;
            touch-action: manipulation;
        }

        .skill-item:hover, .skill-item:active {
            background: rgba(79, 195, 247, 0.15);
            border-color: var(--blue);
            transform: translateX(2px);
        }

        .skill-item.unlocked {
            border-color: var(--blue);
            background: rgba(79, 195, 247, 0.1);
        }

        .skill-item.dragon-skill {
            border-color: var(--red);
            background: rgba(255, 107, 107, 0.1);
        }

        .skill-item.dragon-skill:hover {
            background: rgba(255, 107, 107, 0.2);
            border-color: var(--red);
        }

        .skill-name {
            font-weight: 600;
            color: var(--blue);
            font-size: clamp(0.9rem, 2.5vw, 1.05rem);
        }

        .skill-name.dragon-skill {
            color: var(--red);
        }

        .skill-level {
            color: var(--gold);
            float: right;
            font-weight: 600;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
        }

        .skill-description {
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            color: var(--text-secondary);
            margin-top: 6px;
            line-height: 1.4;
        }

        .button-learn {
            background: linear-gradient(135deg, var(--blue), #29b6f6);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            font-weight: 600;
            margin-top: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
            min-height: 44px;
            touch-action: manipulation;
        }

        .button-learn:hover, .button-learn:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4);
        }

        .button-learn:disabled {
            background: #555;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        /* === ITEM STYLES === */
        .item {
            cursor: grab;
            position: relative;
            transition: all 0.2s ease;
        }

        .item:active {
            cursor: grabbing;
        }

        .item.dragging {
            opacity: 0.6;
            cursor: grabbing;
            transform: scale(1.1);
        }

        .item.common { 
            background: linear-gradient(135deg, #6b7280, #4b5563);
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
        }
        .item.rare { 
            background: linear-gradient(135deg, var(--blue), #2196f3);
            box-shadow: 0 2px 12px rgba(79, 195, 247, 0.4);
        }
        .item.epic { 
            background: linear-gradient(135deg, #9c27b0, #7b1fa2);
            box-shadow: 0 2px 12px rgba(156, 39, 176, 0.4);
        }
        .item.legendary { 
            background: linear-gradient(135deg, #ff9800, #f57c00);
            box-shadow: 0 2px 15px rgba(255, 152, 0, 0.5);
        }
        .item.mythic { 
            background: linear-gradient(135deg, #ff1744, #d50000);
            box-shadow: 0 2px 15px rgba(255, 23, 68, 0.6);
            animation: mythicGlow 2s ease-in-out infinite alternate;
        }

        @keyframes mythicGlow {
            from { box-shadow: 0 2px 15px rgba(255, 23, 68, 0.6); }
            to { box-shadow: 0 4px 25px rgba(255, 23, 68, 0.9), 0 0 15px rgba(255, 23, 68, 0.5); }
        }

        .item-count.large-stack {
            background: rgba(255, 215, 0, 0.9) !important;
            color: #000 !important;
            font-weight: bold;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
            animation: pulseGold 2s ease-in-out infinite alternate;
        }

        @keyframes pulseGold {
            from { box-shadow: 0 0 8px rgba(255, 215, 0, 0.5); }
            to { box-shadow: 0 0 15px rgba(255, 215, 0, 0.8), 0 0 25px rgba(255, 215, 0, 0.3); }
        }

        /* === ACTION BUTTONS === */
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-button {
            flex: 1;
            background: linear-gradient(135deg, var(--blue), #29b6f6);
            color: white;
            border: none;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
            min-height: 44px;
            touch-action: manipulation;
        }

        .action-button:hover, .action-button:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(79, 195, 247, 0.4);
        }

        .action-button.sell {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .action-button.sell:hover {
            box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
        }

        /* === SHOP ITEMS === */
        .shop-item {
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-height: 120px;
            touch-action: manipulation;
        }

        .shop-item:hover, .shop-item:active {
            background: rgba(79, 195, 247, 0.15);
            transform: translateY(-2px);
            border-color: var(--blue);
            box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
        }

        .shop-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .shop-item.disabled:hover {
            transform: none;
            background: var(--glass-bg);
            border-color: var(--border-color);
            box-shadow: none;
        }

        .shop-item-icon {
            font-size: clamp(1.8rem, 5vw, 2.5rem);
            margin-bottom: 8px;
        }

        .shop-item-name {
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--text-primary);
            font-size: clamp(0.8rem, 2vw, 0.9rem);
        }

        .shop-item-level {
            font-size: clamp(0.7rem, 1.8vw, 0.85rem);
            color: var(--text-secondary);
            margin-bottom: 6px;
        }

        .shop-item-price {
            color: var(--gold);
            font-weight: 700;
            font-size: clamp(0.9rem, 2.2vw, 1.1rem);
        }

        /* === MESSAGES AND MODALS === */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            padding: 20px 30px;
            border-radius: 12px;
            border: 2px solid var(--gold);
            font-size: clamp(1rem, 3vw, 1.5rem);
            color: var(--gold);
            display: none;
            z-index: 2000;
            backdrop-filter: blur(15px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.7);
            text-align: center;
            max-width: 90vw;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(10px);
            padding: 20px;
        }

        .modal-content {
            background: var(--glass-bg);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal h2 {
            color: var(--gold);
            margin-bottom: 20px;
            font-size: clamp(1.4rem, 4vw, 2rem);
        }

        .form-group {
            margin: 16px 0;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-weight: 600;
            font-size: clamp(0.9rem, 2.5vw, 1rem);
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: clamp(0.9rem, 2.5vw, 1rem);
            min-height: 44px;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--blue);
            box-shadow: 0 0 15px rgba(79, 195, 247, 0.3);
        }

        .create-character-btn {
            background: linear-gradient(135deg, var(--gold), #ffed4e);
            color: #1a1a2e;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-size: clamp(1rem, 3vw, 1.2rem);
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
            margin-top: 16px;
            min-height: 44px;
            width: 100%;
            touch-action: manipulation;
        }

        .create-character-btn:hover, .create-character-btn:active {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255, 215, 0, 0.5);
        }

        /* === STATUS EFFECTS === */
        #status-effects-container {
            position: absolute;
            top: 60px;
            left: 10px;
            z-index: 10;
            max-width: 200px;
        }

        #status-toggle-btn {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            width: 100%;
            min-height: 44px;
            touch-action: manipulation;
        }

        /* === MOBILE RESPONSIVE === */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            
            .game-container {
                padding: 10px;
                border-radius: 12px;
            }
            
            .main-layout-new {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
                height: auto;
                gap: 12px;
                padding: 0 5px;
            }
            
            .center-column-new {
                order: 1;
            }
            
            .stats-panel {
                order: 2;
            }
            
            .inventory-panel-paginated {
                order: 3;
            }
            
            .inventory-grid-paginated {
                grid-template-columns: repeat(6, 56px);
                grid-template-rows: repeat(6, 56px);
                width: 360px;
                height: 356px;
            }
            
            .inventory-grid-paginated .inventory-slot {
                width: 56px !important;
                height: 56px !important;
            }
            
            .inventory-grid-paginated .item {
                width: 52px !important;
                height: 52px !important;
            }
            
            .inventory-grid-paginated .item img {
                width: 52px !important;
                height: 52px !important;
            }
            
            .inventory-grid-paginated .item:not(:has(img)) {
                font-size: 1.8rem !important;
            }
            
            .shop-items-bottom {
                grid-template-columns: repeat(3, 1fr);
                gap: 8px;
            }
            
            .hotbar-section .skill-hotbar {
                padding: 6px;
                gap: 4px;
            }
            
            .page-btn {
                padding: 6px 10px;
                font-size: 0.9rem;
            }
            
            .inventory-nav {
                gap: 2px;
            }
            
            .hotbar-skill {
                min-height: 50px;
                min-width: 50px;
            }
            
            .stat-item {
                margin: 6px 0;
                padding: 12px;
            }
            
            button, select, input, .inventory-slot, .equipment-slot, .shop-item, .skill-item {
                min-height: 44px;
                min-width: 44px;
            }
            
            input, select, textarea {
                font-size: 16px;
            }
            
            body {
                overflow-x: hidden;
            }
            
            .skills-container, .modal-content {
                -webkit-overflow-scrolling: touch;
            }
            
            #status-effects-container {
                top: 40px;
                left: 5px;
                max-width: 180px;
            }
        }

        /* Landscape mobile adjustments */
        @media (max-width: 768px) and (orientation: landscape) {
            .main-layout-new {
                height: auto;
                grid-template-columns: minmax(250px, 1fr) minmax(400px, 2fr) minmax(300px, 1fr);
                grid-template-rows: none;
            }
            
            .center-column-new, .stats-panel, .inventory-panel-paginated {
                order: initial;
            }
            
            .inventory-grid-paginated {
                grid-template-columns: repeat(8, 48px);
                grid-template-rows: repeat(4, 48px);
                width: 412px;
                height: 212px;
            }
            
            .inventory-grid-paginated .inventory-slot {
                width: 48px !important;
                height: 48px !important;
            }
            
            .inventory-grid-paginated .item {
                width: 44px !important;
                height: 44px !important;
            }
            
            .inventory-grid-paginated .item img {
                width: 44px !important;
                height: 44px !important;
            }
        }

        /* Character selector improvements */
        #character-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
            gap: 12px !important;
        }

        @media (max-width: 480px) {
            #character-grid {
                grid-template-columns: 1fr 1fr !important;
            }
        }

        /* Enhanced animations */
        .inventory-grid-paginated {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-4px); }
            75% { transform: translateX(4px); }
        }

        /* Tooltip adjustments */
        #custom-tooltip, #equipment-tooltip {
            max-width: 90vw;
            font-size: clamp(0.8rem, 2vw, 0.9rem);
            padding: 8px 12px;
        }

        /* Enhanced location type indicators */
        .location-type-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-left: 8px;
        }

        .location-type-indicator.field {
            background: rgba(76, 175, 80, 0.2);
            color: var(--field-color);
            border: 1px solid var(--field-color);
        }

        .location-type-indicator.dungeon {
            background: rgba(255, 152, 0, 0.2);
            color: var(--dungeon-color);
            border: 1px solid var(--dungeon-color);
        }

        .location-type-indicator.advanced {
            background: rgba(255, 107, 107, 0.2);
            color: var(--red);
            border: 1px solid var(--red);
        }

        /* Auto-battle indicators */
        .auto-battle-safe {
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4) !important;
        }

        .auto-battle-warning {
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4) !important;
            animation: warningPulse 2s ease-in-out infinite;
        }

        @keyframes warningPulse {
            0%, 100% { box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4); }
            50% { box-shadow: 0 6px 20px rgba(255, 152, 0, 0.7); }
        }

        /* Enhanced equipment feedback */
        .equipment-slot.drag-over.invalid {
            border-color: #f44336 !important;
            background: rgba(244, 67, 54, 0.2) !important;
            animation: shake 0.3s ease-in-out;
        }

        /* Hide old layout elements */
        .main-layout:not(.main-layout-new),
        .bottom-layout,
        .inventory-grid:not(.inventory-grid-paginated),
        .shop-items:not(.shop-items-bottom) {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Character Creation Modal -->
    <div class="modal" id="character-creation-modal" style="display: none;">
        <div class="modal-content">
            <h2>Create Your Character</h2>
            <form id="character-form">
                <div class="form-group">
                    <label for="character-name">Character Name:</label>
                    <input type="text" id="character-name" placeholder="Enter your name" maxlength="20" required>
                </div>
                <div class="form-group">
                    <label for="character-class">Class:</label>
                    <select id="character-class">
                        <option value="swordsman">Swordsman - Balanced melee warrior (Auto-battle focused)</option>
                        <option value="mage">Mage - Powerful spellcaster (Active skill focused)</option>
                    </select>
                    <div id="class-description" style="color: var(--text-secondary); margin-top: 8px; font-size: 0.9rem; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 4px;">
                        <strong>Swordsman:</strong> A balanced warrior focused on physical combat. Great for auto-battle with steady damage and good defense. Can rebirth into Dragon Knight at level 100.
                    </div>
                </div>
                <button type="submit" class="create-character-btn">Start Adventure!</button>
            </form>
        </div>
    </div>

    <div class="game-container">
        <div class="header">
            <h1>Ragnarok Online</h1>
            <div class="subtitle">Enhanced Clicker RPG</div>
            <button class="characters-button" onclick="Game.characterManager.showCharacterSelector()">👥</button>
        </div>

        <div class="main-layout-new">
    <!-- LEFT COLUMN: Player Stats (unchanged) -->
    <div class="panel stats-panel">
        <h2>Character Stats</h2>
        <div class="gold-display">
            💰 <span id="player-gold">100</span> Gold
        </div>
        <div class="stat-item">
            <span class="stat-label">Name:</span>
            <span class="stat-value" id="player-name">Adventurer</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Class:</span>
            <span class="stat-value" id="player-class">Swordsman</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Level:</span>
            <span class="stat-value" id="player-level">1</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">HP:</span>
            <span class="stat-value" id="player-hp">150/150</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">MP:</span>
            <span class="stat-value" id="player-mp">50/50</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">ATK:</span>
            <span class="stat-value" id="player-atk">25</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">DEF:</span>
            <span class="stat-value" id="player-def">0</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Crit Chance:</span>
            <span class="stat-value" id="player-crit-chance">15%</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">Crit Damage:</span>
            <span class="stat-value" id="player-crit-damage">200%</span>
        </div>

        <h2 style="margin-top: 16px;">Equipment</h2>
        <div class="equipment-slots">
            <div class="equipment-slot" data-slot="helmet" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Helmet</div>
            </div>
            <div class="equipment-slot" data-slot="chestplate" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Chest</div>
            </div>
            <div class="equipment-slot" data-slot="sword" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Weapon</div>
            </div>
            <div class="equipment-slot" data-slot="shield" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Shield</div>
            </div>
            <div class="equipment-slot" data-slot="leggings" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Leggings</div>
            </div>
            <div class="equipment-slot" data-slot="boots" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Boots</div>
            </div>
        </div>

        <h3 style="margin-top: 16px;">Accessories</h3>
        <div class="equipment-slots" style="grid-template-columns: repeat(3, 1fr);">
            <div class="equipment-slot" data-slot="ring1" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Ring 1</div>
            </div>
            <div class="equipment-slot" data-slot="ring2" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Ring 2</div>
            </div>
            <div class="equipment-slot" data-slot="necklace" ondrop="Game.equipment.dropEquipment(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Necklace</div>
            </div>
        </div>

        <!-- Equipment Upgrade Section -->
        <h2 style="margin-top: 16px;">Equipment Upgrade</h2>
        <div class="upgrade-section">
            <div class="upgrade-slot" id="upgrade-slot" ondrop="Game.equipment.dropUpgradeItem(event)" ondragover="Game.equipment.allowDrop(event)" ondragleave="Game.equipment.dragLeave(event)">
                <div class="slot-label">Drop Equipment Here</div>
            </div>
            <div class="upgrade-info" id="upgrade-info" style="display: none;">
                <div class="upgrade-stats" id="upgrade-stats"></div>
                <div class="upgrade-cost" id="upgrade-cost"></div>
                <div class="upgrade-chance" id="upgrade-chance"></div>
            </div>
            <button class="upgrade-button" id="upgrade-button" onclick="Game.equipment.upgradeEquipment()" disabled>
                ⚡ Upgrade Equipment
            </button>
        </div>
    </div>

    <!-- CENTER COLUMN: Battle Area with Controls + Hotbar -->
    <div class="center-column-new">
        <!-- Enhanced Location Selector -->
        <div class="panel location-panel-compact">
            <div class="location-selector">
                <select id="location-select" onchange="Game.combat.changeLocation()">
                    <!-- Will be populated by JavaScript with enhanced data -->
                </select>
            </div>
        </div>

        <!-- Player HP & MP Bars -->
        <div class="panel player-status-compact" style="min-height: 180px;">
            <h2>Player Status</h2>
            <div class="player-bars-container">
                <div class="player-hp-label">Your HP</div>
                <div class="player-hp-bar">
                    <div class="player-hp-fill" id="player-hp-fill" style="width: 100%"></div>
                    <div class="player-hp-text">
                        <span id="player-current-hp">150</span> / <span id="player-max-hp">150</span>
                    </div>
                </div>

                <div class="player-mp-label">Your MP</div>
                <div class="player-mp-bar">
                    <div class="player-mp-fill" id="player-mp-fill" style="width: 100%"></div>
                    <div class="player-mp-text">
                        <span id="player-current-mp">50</span> / <span id="player-max-mp">50</span>
                    </div>
                </div>

                <!-- EXP Bar moved here -->
                <div class="player-exp-label" style="margin-top: 8px;">Experience</div>
                <div class="exp-bar">
                    <div class="exp-fill" id="exp-fill" style="width: 0%"></div>
                </div>
                <div style="text-align: center; font-size: 0.8em; color: var(--text-secondary); margin-top: 2px;">
                    <span id="current-exp">0</span> / <span id="max-exp">100</span> EXP
                </div>
            </div>
        </div>

        <!-- Battle Area (MAIN ENHANCED COMBAT AREA) -->
        <div class="panel battle-area">
            <!-- Enhanced combat will replace everything in here -->
            <!-- Don't put any critical UI elements here -->
        </div>

        <!-- Hotbar directly under combat area -->
        <div class="hotbar-section">
            <div class="skill-hotbar" id="skill-hotbar">
                <!-- Skills will be added dynamically -->
            </div>
        </div>

        <!-- Skills Panel (moved to center column, compact) -->
        <div class="panel skills-panel-compact">
            <h2>Skills</h2>
            <div class="skill-points-display" style="margin-bottom: 12px; padding: 8px; background: rgba(255, 215, 0, 0.1); border: 1px solid var(--gold); border-radius: 4px; text-align: center;">
                <span style="color: var(--gold); font-weight: bold;">Skill Points: </span>
                <span id="player-skill-points" style="color: var(--gold); font-weight: bold;">0</span>
            </div>
            <div id="skills-container">
                <p style="color: var(--text-secondary); text-align: center;">Skills loading...</p>
            </div>
        </div>
    </div>

    <!-- RIGHT COLUMN: Inventory with 5 Pages -->
    <div class="panel inventory-panel-paginated">
        <h2>Inventory</h2>
        
        <!-- Inventory Page Navigation -->
        <div class="inventory-nav">
            <button class="page-btn active" data-page="0">1</button>
            <button class="page-btn" data-page="1">2</button>
            <button class="page-btn" data-page="2">3</button>
            <button class="page-btn" data-page="3">4</button>
            <button class="page-btn" data-page="4">5</button>
            <button class="page-btn favorites-btn" data-page="5" title="Favorites - Full inventory page for protected items">⭐</button>
        </div>
        
        <!-- Inventory Grid (20 slots per page: 5x4 grid) -->
        <div class="inventory-grid-paginated" id="inventory-grid">
            <!-- 20 slots will be generated by JavaScript per page -->
        </div>
        
        <!-- Inventory Management Controls -->
        <div class="inventory-controls" style="margin-bottom: 10px; padding: 8px; background: rgba(0,0,0,0.3); border-radius: 4px;">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                <label style="color: var(--text-primary); font-size: 0.9rem;">Sort by:</label>
                <select id="sort-type" onchange="Game.equipment.setSortType(this.value)" style="padding: 4px; background: var(--bg-secondary); color: var(--text-primary); border: 1px solid var(--border); border-radius: 3px;">
                    <option value="type">Item Type</option>
                    <option value="rarity">Rarity</option>
                    <option value="price">Gold Value</option>
                    <option value="level">Required Level</option>
                </select>
                <button class="action-button" onclick="Game.equipment.sortInventory()" style="padding: 4px 8px; font-size: 0.8rem;">📋 Sort</button>
            </div>
            <div style="display: flex; align-items: center; gap: 5px;">
                <input type="checkbox" id="allow-sell-valuable" onchange="Game.equipment.setAllowSellValuable(this.checked)" style="margin: 0;">
                <label for="allow-sell-valuable" style="color: var(--text-secondary); font-size: 0.85rem;">Allow selling Epic+ and upgraded items</label>
            </div>
        </div>

        <div class="action-buttons">
            <button class="action-button sell" onclick="Game.equipment.sellAllItems()">💰 Sell All</button>
            <button class="action-button" onclick="Game.equipment.usePotion()">🧪 Use Potion (Q)</button>
            <button class="action-button" onclick="Game.equipment.useMpPotion()">💙 Use MP Potion (E)</button>
        </div>
    </div>
</div>

<!-- Shop at the very bottom of the screen -->
<div class="shop-section-bottom">
    <div class="panel shop-panel-bottom">
        <h2>Shop</h2>
        <div class="shop-items-bottom" id="shop-items">
            <!-- Shop items will be populated by JavaScript -->
        </div>
    </div>
</div>

    <div class="message" id="message"></div>

    <!-- Load JavaScript Modules -->
    <script src="js/data/monsters.js"></script>
    <script src="js/data/classes.js"></script>
    <script src="js/data/skills.js"></script>
    <script src="js/core/player.js"></script>
    <script src="js/core/combat.js"></script>
    <script src="js/core/equipment.js"></script>
    <script src="js/ui/display.js"></script>
    <script src="js/main.js"></script>
    <script src="js/ui/enhanced-combat.js"></script>
</body>
</html>